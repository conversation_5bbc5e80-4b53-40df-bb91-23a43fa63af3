<script>
	import store from './store'

	export default {
		onLaunch: async function() {
			console.log('采茶记录管理系统启动')

			try {
				// 初始化应用
				await store.dispatch('initApp')
				console.log('应用初始化完成')

				// 初始化销售记录系统
				const salesInitializer = new SalesDataInitializer()
				const result = await salesInitializer.initializeSalesSystem()

				if (result.success) {
					console.log('销售记录系统初始化成功:', result.data)
				} else {
					console.warn('销售记录系统初始化失败:', result.message)
				}
			} catch (error) {
				console.error('应用初始化失败:', error)
			}
		},
		onShow: function() {
			console.log('App Show')
			// 应用从后台进入前台时，检查网络状态
			if (store) {
				store.dispatch('app/checkNetworkStatus')
			}
		},
		onHide: function() {
			console.log('App Hide')
		},
		onError: function(error) {
			console.error('应用发生错误:', error)
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	// 设置整个项目的背景色
	page {
		background-color: #f5f5f5;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}
</style>
