<template>
	<view class="personal-stats-page">

		<!-- 用户选择器 - 重新设计 -->
		<view class="enhanced-user-selector" v-if="isAdmin">
			<view class="selector-card">
				<view class="selector-header">
					<text class="selector-icon">👥</text>
					<text class="selector-title">查看用户</text>
				</view>
				<picker :range="workerNames" :value="selectedWorkerIndex" @change="onWorkerChange">
					<view class="enhanced-selector-input">
						<text class="selected-user">{{ selectedWorker || '所有工人' }}</text>
						<text class="selector-arrow">▼</text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 错误状态 - 重新设计 -->
		<view class="enhanced-error-container" v-if="!personalStats || Object.keys(personalStats).length === 0">
			<view class="error-card">
				<text class="error-icon">📊</text>
				<text class="error-title">暂无数据</text>
				<text class="error-desc">当前时间范围内没有工作记录</text>
				<button class="enhanced-retry-btn" @click="loadPersonalStats">
					<text class="btn-icon">🔄</text>
					<text>刷新数据</text>
				</button>
			</view>
		</view>

		<view class="stats-content" v-else>
			<!-- 时间范围选择 - 重新设计 -->
			<view class="enhanced-time-selector">
				<view class="time-selector-card">
					<view class="time-selector-header">
						<text class="time-icon">📅</text>
						<text class="time-title">时间范围</text>
					</view>

					<view class="enhanced-range-tabs">
						<view class="enhanced-range-tab" :class="{ active: timeRange === 'week' }"
							@click="setTimeRange('week')">
							<text class="tab-icon">📅</text>
							<text class="tab-text">本周</text>
						</view>
						<view class="enhanced-range-tab" :class="{ active: timeRange === 'month' }"
							@click="setTimeRange('month')">
							<text class="tab-icon">📆</text>
							<text class="tab-text">本月</text>
						</view>
						<view class="enhanced-range-tab" :class="{ active: timeRange === 'custom' }"
							@click="setTimeRange('custom')">
							<text class="tab-icon">🗓️</text>
							<text class="tab-text">自定义</text>
						</view>
					</view>

					<!-- 自定义时间范围 - 重新设计 -->
					<view class="enhanced-custom-range" v-if="timeRange === 'custom'">
						<view class="date-picker-container">
							<picker mode="date" :value="customStartDate" @change="onStartDateChange">
								<view class="enhanced-date-picker">
									<text class="date-icon">📅</text>
									<text class="date-text">{{ customStartDate || '开始日期' }}</text>
								</view>
							</picker>
							<view class="date-separator">
								<text class="separator-icon">→</text>
							</view>
							<picker mode="date" :value="customEndDate" @change="onEndDateChange">
								<view class="enhanced-date-picker">
									<text class="date-icon">📅</text>
									<text class="date-text">{{ customEndDate || '结束日期' }}</text>
								</view>
							</picker>
						</view>
					</view>
				</view>
			</view>

			<!-- 快速统计卡片 -->
			<QuickStatsCard :stats="personalStats || {}" @stat-click="onStatClick" />

			<!-- 操作按钮 - 重新设计 -->
			<view class="enhanced-action-section" v-if="hasData">
				<view class="action-buttons-card">
					<view class="action-buttons-grid">
						<button class="enhanced-action-btn export-btn" @click="exportData">
							<view class="btn-content">
								<text class="btn-icon">📤</text>
								<text class="btn-text">导出数据</text>
							</view>
						</button>
						<button class="enhanced-action-btn print-btn" @click="printReport">
							<view class="btn-content">
								<text class="btn-icon">🖨️</text>
								<text class="btn-text">打印报告</text>
							</view>
						</button>
					</view>
				</view>
			</view>

			<!-- 工作记录列表-->
			<view class="enhanced-records-section" v-if="hasData">
				<view class="records-header">
					<view class="records-title">
						<text class="records-icon">📋</text>
						<text class="records-text">工作记录</text>
					</view>
					<view class="records-count">
						<text class="count-text">共 {{ workerRecords.length }} 条记录</text>
					</view>
				</view>

				<view class="enhanced-record-list">
					<view class="enhanced-record-item" :data-work-mode="record.work_mode"
						v-for="record in workerRecords" :key="record.id" @click="viewRecord(record)">
						<view class="record-card">
							<view class="record-header">
								<view class="record-date-section">
									<text class="date-icon">📅</text>
									<text class="date-text">{{ $formatDate(record.date) }}</text>
								</view>
								<view class="work-mode-badge" :class="record.work_mode">
									<text class="mode-icon">{{ getModeIcon(record.work_mode) }}</text>
									<text class="mode-text">{{ getModeText(record.work_mode) }}</text>
								</view>
							</view>

							<view class="enhanced-record-content">
								<!-- 采茶模式详情 -->
								<view class="enhanced-record-details tea-picking"
									v-if="record.work_mode === 'tea_picking'">
									<view class="detail-grid">
										<view class="detail-card">
											<text class="detail-icon">🍃</text>
											<view class="detail-info">
												<text class="detail-label">项目类型</text>
												<text class="detail-value">{{ getTeaProjectSummary(record) }}</text>
											</view>
										</view>
										<view class="detail-card">
											<text class="detail-icon">⚖️</text>
											<view class="detail-info">
												<text class="detail-label">原斤数</text>
												<text class="detail-value">{{ getTeaOriginalWeight(record) }}斤</text>
											</view>
										</view>
										<view class="detail-card">
											<text class="detail-icon">📊</text>
											<view class="detail-info">
												<text class="detail-label">实际斤数</text>
												<text class="detail-value">{{ getTeaActualWeight(record) }}斤</text>
											</view>
										</view>
										<view class="detail-card">
											<text class="detail-icon">💰</text>
											<view class="detail-info">
												<text class="detail-label">平均单价</text>
												<text class="detail-value">¥{{ getTeaAveragePrice(record) }}/斤</text>
											</view>
										</view>
									</view>
								</view>

								<!-- 时工模式详情 -->
								<view class="enhanced-record-details hourly" v-if="record.work_mode === 'hourly'">
									<view class="detail-grid">
										<view class="detail-card">
											<text class="detail-icon">⏰</text>
											<view class="detail-info">
												<text class="detail-label">工作时长</text>
												<text class="detail-value">{{ getHourlyTotalHours(record) }}小时</text>
											</view>
										</view>
										<view class="detail-card">
											<text class="detail-icon">💵</text>
											<view class="detail-info">
												<text class="detail-label">平均时薪</text>
												<text class="detail-value">¥{{ getHourlyAverageRate(record) }}/小时</text>
											</view>
										</view>
									</view>
								</view>
							</view>

							<view class="enhanced-record-footer">
								<view class="earnings-section">
									<text class="earnings-label">总工钱</text>
									<text class="earnings-value">¥{{ $formatCurrency(record.total_earnings) }}</text>
								</view>
								<view class="view-detail-btn">
									<text class="detail-btn-text">查看详情</text>
									<text class="detail-btn-icon">→</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 - 重新设计 -->
		<view class="enhanced-empty-state" v-if="!hasData">
			<view class="empty-card">
				<text class="empty-icon">📊</text>
				<text class="empty-title">暂无统计数据</text>
				<text class="empty-desc">{{ emptyHint }}</text>
				<view class="empty-actions">
					<button class="empty-action-btn" @click="refreshStats">
						<text class="empty-btn-icon">🔄</text>
						<text class="empty-btn-text">刷新数据</text>
					</button>
				</view>
			</view>
		</view>
	</view> <!-- 结束 stats-content -->

	<!-- 工作记录详情弹窗 -->
	<view class="record-detail-modal" v-if="showDetailModal" @click="closeDetailModal">
		<view class="modal-content" @click.stop>
			<view class="modal-header">
				<text class="modal-title">工作记录详情</text>
				<view class="close-btn" @click="closeDetailModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="modal-body" v-if="selectedRecord">
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="section-header">
						<text class="section-title">📋 基本信息</text>
					</view>
					<view class="info-grid">
						<view class="info-item">
							<text class="info-label">日期</text>
							<text class="info-value">{{ selectedRecord.date }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">工人</text>
							<text class="info-value">{{ selectedRecord.worker_name }}</text>
						</view>
						<view class="info-item full-width">
							<text class="info-label">工作模式</text>
							<view class="work-mode-tag" :class="selectedRecord.work_mode">
								<text>{{ getModeText(selectedRecord.work_mode) }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 采茶模式详情 -->
				<view class="detail-section"
					v-if="selectedRecord.work_mode === 'tea_picking' && selectedRecord.tea_picking_details">
					<view class="section-header">
						<text class="section-title">🍃 采茶详情</text>
					</view>
					<view class="work-details">
						<view class="detail-grid">
							<view class="detail-item">
								<text class="detail-label">时间段</text>
								<text class="detail-value">{{
									getTimePeriodText(selectedRecord.tea_picking_details.time_period) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">项目类型</text>
								<text class="detail-value">{{ getProjectText(selectedRecord.tea_picking_details.project)
								}}</text>
							</view>
							<view class="detail-item highlight">
								<text class="detail-label">重量</text>
								<text class="detail-value">{{ selectedRecord.tea_picking_details.weight }} kg</text>
							</view>
							<view class="detail-item highlight">
								<text class="detail-label">单价</text>
								<text class="detail-value">¥{{ formatCurrency(selectedRecord.tea_picking_details.price)
								}}/kg</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 时工模式详情 -->
				<view class="detail-section"
					v-if="selectedRecord.work_mode === 'hourly' && selectedRecord.hourly_work_details">
					<view class="section-header">
						<text class="section-title">⏰ 时工详情</text>
					</view>
					<view class="work-details">
						<view class="time-range">
							<view class="time-item">
								<text class="time-label">开始时间</text>
								<text class="time-value">{{ selectedRecord.hourly_work_details.start_time }}</text>
							</view>
							<text class="time-separator">-</text>
							<view class="time-item">
								<text class="time-label">结束时间</text>
								<text class="time-value">{{ selectedRecord.hourly_work_details.end_time }}</text>
							</view>
						</view>

						<view class="detail-grid">
							<view class="detail-item highlight">
								<text class="detail-label">总工时</text>
								<text class="detail-value">{{ selectedRecord.hourly_work_details.total_hours }}h</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">常规工时</text>
								<text class="detail-value">{{ selectedRecord.hourly_work_details.regular_hours
								}}h</text>
							</view>
							<view class="detail-item overtime"
								v-if="selectedRecord.hourly_work_details.overtime_hours > 0">
								<text class="detail-label">加班工时</text>
								<text class="detail-value">{{ selectedRecord.hourly_work_details.overtime_hours
								}}h</text>
							</view>
							<view class="detail-item highlight">
								<text class="detail-label">时薪</text>
								<text class="detail-value">¥{{
									formatCurrency(selectedRecord.hourly_work_details.hourly_rate) }}/h</text>
							</view>
							<view class="detail-item overtime" v-if="selectedRecord.hourly_work_details.overtime_rate">
								<text class="detail-label">加班时薪</text>
								<text class="detail-value">¥{{
									formatCurrency(selectedRecord.hourly_work_details.overtime_rate) }}/h</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 收入详情 -->
				<view class="detail-section">
					<view class="section-header">
					</view>
					<view class="earnings-details">
						<view class="earnings-breakdown"
							v-if="selectedRecord.work_mode === 'hourly' && selectedRecord.hourly_work_details">
							<view class="earnings-item">
								<text class="earnings-label">常规收入</text>
								<text class="earnings-value">¥{{
									formatCurrency(selectedRecord.hourly_work_details.regular_earnings || 0) }}</text>
							</view>
							<view class="earnings-item overtime"
								v-if="selectedRecord.hourly_work_details.overtime_earnings > 0">
								<text class="earnings-label">加班收入</text>
								<text class="earnings-value">¥{{
									formatCurrency(selectedRecord.hourly_work_details.overtime_earnings) }}</text>
							</view>
						</view>
						<view class="total-earnings">
							<text class="total-label">总收入</text>
							<text class="total-value">¥{{ formatCurrency(selectedRecord.total_earnings) }}</text>
						</view>
					</view>
				</view>

				<!-- 备注信息 -->
				<view class="detail-section" v-if="selectedRecord.notes">
					<view class="section-header">
						<text class="section-title">📝 备注</text>
					</view>
					<view class="notes-content">
						<text class="notes-text">{{ selectedRecord.notes }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import QuickStatsCard from '@/components/QuickStatsCard.vue'
import printManager from '@/utils/print.js'

export default {
	name: 'PersonalStatsPage',
	components: {
		QuickStatsCard
	},
	data() {
		return {
			timeRange: 'month',
			customStartDate: '',
			customEndDate: '',
			selectedWorker: '所有工人',
			selectedWorkerIndex: 0,
			workerNames: ['所有工人'],
			// 详情弹窗相关
			showDetailModal: false,
			selectedRecord: null
		}
	},
	computed: {
		...mapGetters('user', ['userInfo', 'isAdmin']),
		...mapGetters('statistics', ['personalStats']),

		hasData() {
			return this.personalStats && this.personalStats.totalRecords > 0
		},

		headerSubtitle() {
			if (this.isAdmin && this.selectedWorker && this.selectedWorker !== '所有工人') {
				return `${this.selectedWorker} · ${this.getPeriodText()}`
			} else if (this.isAdmin) {
				return `所有工人 · ${this.getPeriodText()}`
			} else {
				return `${this.userInfo.real_name || this.userInfo.username} · ${this.getPeriodText()}`
			}
		},

		emptyHint() {
			if (this.isAdmin) {
				return this.selectedWorker ? `${this.selectedWorker}在此时间段内暂无工作记录` : '请选择要查看的工人'
			} else {
				return '您在此时间段内暂无工作记录'
			}
		},

		workerRecords() {
			// 获取当前工人的所有记录，按日期倒序排列
			const records = (this.personalStats && this.personalStats.records) || []
			return records.sort((a, b) => new Date(b.date) - new Date(a.date))
		}
	},
	onLoad() {
		this.initPage()
	},
	onShow() {
		// 如果是管理员且工人列表已加载，恢复保存的选择
		if (this.isAdmin && this.workerNames.length > 1) {
			this.restoreSavedWorkerSelection()
		}
		this.refreshStats()
	},
	methods: {
		...mapActions('statistics', ['fetchPersonalStats']),

		async initPage() {
			// 如果是普通用户，设置为查看自己的数据
			if (!this.isAdmin) {
				this.selectedWorker = this.userInfo.real_name || this.userInfo.username
			} else {
				// 管理员需要先获取工人列表
				await this.loadWorkerNames()
				// 恢复保存的工人选择
				this.restoreSavedWorkerSelection()
			}

			await this.refreshStats()
		},

		async loadWorkerNames() {
			try {
				// TODO: 从API获取工人姓名列表
				const nameLibrary = uni.getStorageSync('nameLibrary') || []
				this.workerNames = ['所有工人', ...nameLibrary]
			} catch (error) {
				console.error('获取工人列表失败:', error)
			}
		},

		// 保存工人选择到本地存储
		saveWorkerSelection() {
			try {
				const selectionData = {
					selectedWorker: this.selectedWorker,
					selectedWorkerIndex: this.selectedWorkerIndex,
					timestamp: Date.now()
				}
				uni.setStorageSync('selectedWorkerPersonalStats', selectionData)
			} catch (error) {
				console.error('保存工人选择失败:', error)
			}
		},

		// 恢复保存的工人选择
		restoreSavedWorkerSelection() {
			try {
				const savedSelection = uni.getStorageSync('selectedWorkerPersonalStats')

				if (savedSelection && savedSelection.selectedWorker) {
					// 检查保存的工人是否仍在工人列表中
					const workerIndex = this.workerNames.indexOf(savedSelection.selectedWorker)

					if (workerIndex !== -1) {
						// 工人存在，恢复选择
						this.selectedWorker = savedSelection.selectedWorker
						this.selectedWorkerIndex = workerIndex
					} else {
						// 工人不存在，回退到默认选择
						this.setDefaultWorkerSelection()
					}
				} else {
					// 没有保存的选择，使用默认值
					this.setDefaultWorkerSelection()
				}
			} catch (error) {
				console.error('恢复工人选择失败:', error)
				this.setDefaultWorkerSelection()
			}
		},

		// 设置默认工人选择
		setDefaultWorkerSelection() {
			this.selectedWorker = '所有工人'
			this.selectedWorkerIndex = 0
		},

		async refreshStats() {
			if (!this.selectedWorker && this.isAdmin) return

			try {
				const params = this.buildStatsParams()
				await this.fetchPersonalStats(params)
			} catch (error) {
				console.error('获取统计数据失败:', error)
				this.$showToast('数据加载失败', 'error')
			}
		},

		buildStatsParams() {
			const params = {}

			// 设置查询的工人
			if (this.isAdmin && this.selectedWorker && this.selectedWorker !== '所有工人') {
				params.workerName = this.selectedWorker
			} else if (!this.isAdmin) {
				params.workerName = this.userInfo.real_name || this.userInfo.username
			}

			// 设置时间范围
			const now = new Date()
			switch (this.timeRange) {
				case 'week':
					const weekStart = new Date(now)
					weekStart.setDate(now.getDate() - now.getDay())
					params.startDate = weekStart.toISOString().split('T')[0]
					params.endDate = now.toISOString().split('T')[0]
					break
				case 'month':
					const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
					params.startDate = monthStart.toISOString().split('T')[0]
					params.endDate = now.toISOString().split('T')[0]
					break
				case 'custom':
					if (this.customStartDate && this.customEndDate) {
						params.startDate = this.customStartDate
						params.endDate = this.customEndDate
					}
					break
			}

			return params
		},

		setTimeRange(range) {
			this.timeRange = range
			this.refreshStats()
		},

		onWorkerChange(e) {
			this.selectedWorkerIndex = e.detail.value
			this.selectedWorker = this.workerNames[this.selectedWorkerIndex]

			// 保存工人选择到本地存储
			this.saveWorkerSelection()

			this.refreshStats()
		},

		onStartDateChange(e) {
			this.customStartDate = e.detail.value
			if (this.customEndDate) {
				this.refreshStats()
			}
		},

		onEndDateChange(e) {
			this.customEndDate = e.detail.value
			if (this.customStartDate) {
				this.refreshStats()
			}
		},

		onStatClick() {
			// 处理统计卡片点击事件
		},

		async exportData() {
			try {
				this.$showLoading('导出中...')

				// TODO: 实现数据导出功能
				await new Promise(resolve => setTimeout(resolve, 2000))

				this.$showToast('导出成功', 'success')
			} catch (error) {
				console.error('导出失败:', error)
				this.$showToast('导出失败', 'error')
			} finally {
				this.$hideLoading()
			}
		},

		async printReport() {
			try {
				if (!this.hasData) {
					this.$showToast('没有可打印的数据', 'none')
					return
				}

				const workerName = this.selectedWorker || this.userInfo.real_name || this.userInfo.username
				const period = this.getPeriodText()

				await printManager.printPersonalStats(this.personalStats, workerName, period)
				this.$showToast('打印任务已发送', 'success')
			} catch (error) {
				console.error('打印失败:', error)
				this.$showToast('打印失败', 'error')
			}
		},

		getPeriodText() {
			switch (this.timeRange) {
				case 'week': return '本周'
				case 'month': return '本月'
				case 'custom':
					if (this.customStartDate && this.customEndDate) {
						return `${this.customStartDate} 至 ${this.customEndDate}`
					}
					return '自定义时间段'
				default: return '统计期间'
			}
		},

		// 记录相关方法
		viewRecord(record) {
			console.log('查看月报记录详情:', record)

			// 根据工作模式跳转到对应的详情页面，并标识来源为月报
			let detailUrl = ''
			if (record.work_mode === 'tea_picking') {
				detailUrl = `/pages/record/detail-tea?id=${record.id}&source=monthly`
			} else if (record.work_mode === 'hourly') {
				detailUrl = `/pages/record/detail-hourly?id=${record.id}&source=monthly`
			} else {
				uni.showToast({
					title: '未知的工作模式',
					icon: 'error'
				})
				return
			}

			uni.navigateTo({
				url: detailUrl,
				fail: function (err) {
					console.error('跳转到详情页面失败:', err)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'error'
					})
				}
			})
		},

		closeDetailModal() {
			this.showDetailModal = false
			this.selectedRecord = null
		},

		// 辅助方法：获取采茶详情数组
		getTeaDetailsArray(record) {
			if (!record.tea_picking_details) {
				return []
			}

			// 如果tea_picking_details已经是数组，直接返回
			if (Array.isArray(record.tea_picking_details)) {
				return record.tea_picking_details
			}

			// 如果tea_picking_details是对象，检查是否包含数组字段
			if (typeof record.tea_picking_details === 'object') {
				// 检查是否有tea_picking_details字段（嵌套结构）
				if (record.tea_picking_details.tea_picking_details && Array.isArray(record.tea_picking_details.tea_picking_details)) {
					return record.tea_picking_details.tea_picking_details
				}

				// 检查是否是类数组对象（有数字键的对象）
				const keys = Object.keys(record.tea_picking_details)
				const numericKeys = keys.filter(key => /^\d+$/.test(key)).sort((a, b) => parseInt(a) - parseInt(b))

				if (numericKeys.length > 0) {
					// 将类数组对象转换为真正的数组
					return numericKeys.map(key => record.tea_picking_details[key])
				}

				// 如果对象本身就是一个详情记录，包装成数组
				if (record.tea_picking_details.project || record.tea_picking_details.original_weight) {
					return [record.tea_picking_details]
				}
			}

			return []
		},

		// 获取采茶项目类型汇总
		getTeaProjectSummary(record) {
			const teaDetails = this.getTeaDetailsArray(record)
			if (!teaDetails || teaDetails.length === 0) {
				return '无'
			}

			const projectCounts = {}
			teaDetails.forEach(detail => {
				const project = this.getProjectText(detail.project)
				projectCounts[project] = (projectCounts[project] || 0) + 1
			})

			return Object.entries(projectCounts)
				.map(([project, count]) => count > 1 ? `${project}×${count}` : project)
				.join('、')
		},

		// 获取原斤数总和
		getTeaOriginalWeight(record) {
			const teaDetails = this.getTeaDetailsArray(record)
			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			const totalOriginalWeight = teaDetails.reduce((sum, detail) => {
				return sum + (parseFloat(detail.original_weight) || 0)
			}, 0)

			return totalOriginalWeight.toFixed(2)
		},

		// 获取实际斤数总和
		getTeaActualWeight(record) {
			const teaDetails = this.getTeaDetailsArray(record)
			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			const totalActualWeight = teaDetails.reduce((sum, detail) => {
				const originalWeight = parseFloat(detail.original_weight) || 0
				const moistureRate = parseFloat(detail.moisture_rate) || 0
				const actualWeight = originalWeight * (1 - moistureRate / 100) // 基于扣水率计算实际重量
				return sum + actualWeight
			}, 0)

			return totalActualWeight.toFixed(2)
		},

		// 获取加权平均单价
		getTeaAveragePrice(record) {
			const teaDetails = this.getTeaDetailsArray(record)
			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			let totalEarnings = 0
			let totalWeight = 0

			teaDetails.forEach(detail => {
				const originalWeight = parseFloat(detail.original_weight) || 0
				const moistureRate = parseFloat(detail.moisture_rate) || 0
				const actualWeight = originalWeight * (1 - moistureRate / 100) // 基于扣水率计算实际重量
				const price = parseFloat(detail.price) || 0
				totalEarnings += actualWeight * price
				totalWeight += actualWeight
			})

			if (totalWeight === 0) {
				return '0.00'
			}

			return (totalEarnings / totalWeight).toFixed(2)
		},

		// 获取时工总工时
		getHourlyTotalHours(record) {
			if (!record.hourly_work_details) {
				return '0.00'
			}

			const details = record.hourly_work_details
			if (!details.is_detail_mode) {
				// 简化模式
				return parseFloat(details.simple_mode?.work_hours || 0).toFixed(2)
			} else {
				// 详细模式
				const morning = parseFloat(details.detail_mode?.morning?.work_hours || 0)
				const afternoon = parseFloat(details.detail_mode?.afternoon?.work_hours || 0)
				const overtime = parseFloat(details.detail_mode?.overtime?.work_hours || 0)
				return (morning + afternoon + overtime).toFixed(2)
			}
		},

		// 获取时工平均时薪
		getHourlyAverageRate(record) {
			const totalHours = parseFloat(this.getHourlyTotalHours(record))
			if (totalHours === 0) {
				return '0.00'
			}

			return (parseFloat(record.total_earnings) / totalHours).toFixed(2)
		},

		getModeText(mode) {
			return mode === 'tea_picking' ? '采茶' : '时工'
		},

		getModeIcon(mode) {
			return mode === 'tea_picking' ? '🍃' : '⏰'
		},

		getTimePeriodText(period) {
			return period === 'morning' ? '上午' : '下午'
		},

		getProjectText(project) {
			const projectMap = {
				'one': '一叶',
				'two': '二叶',
				'three': '三叶'
			}
			return projectMap[project] || project
		},

		// 格式化方法
		formatDetailDate(date) {
			if (!date) return ''
			const d = new Date(date)
			const year = d.getFullYear()
			const month = String(d.getMonth() + 1).padStart(2, '0')
			const day = String(d.getDate()).padStart(2, '0')
			const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			const weekday = weekdays[d.getDay()]
			return `${year}年${month}月${day}日 ${weekday}`
		},

		formatCurrency(amount) {
			if (amount === null || amount === undefined) return '0.00'
			return Number(amount).toFixed(2)
		},

		// 工具方法
		$formatDate(date) {
			if (!date) return ''
			const d = new Date(date)
			const month = String(d.getMonth() + 1).padStart(2, '0')
			const day = String(d.getDate()).padStart(2, '0')
			return `${month}-${day}`
		},

		$formatCurrency(amount) {
			return this.formatCurrency(amount)
		},

		$formatTime(time) {
			if (!time) return ''
			return time
		},

		$showLoading(title) {
			uni.showLoading({
				title: title || '加载中...'
			})
		},

		$hideLoading() {
			uni.hideLoading()
		},

		$showToast(title, icon = 'none') {
			uni.showToast({
				title,
				icon: icon === 'error' ? 'none' : icon
			})
		}
	}
}
</script>

<style scoped>
/* 页面基础样式 */
.personal-stats-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding-bottom: 40rpx;
}

/* 现代化页面头部 */
.page-header {
	position: relative;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 30rpx 40rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
}

.header-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.header-content {
	position: relative;
	z-index: 1;
}

.header-title {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.title-icon {
	font-size: 48rpx;
}

.title-text {
	font-size: 42rpx;
	font-weight: 700;
	color: white;
}

.header-subtitle {
	opacity: 0.9;
}

.header-subtitle text {
	font-size: 28rpx;
	color: white;
	font-weight: 400;
}

/* 增强的用户选择器 */
.enhanced-user-selector {
	margin: 0 0 20rpx;
}

.selector-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.selector-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 20rpx;
}

.selector-icon {
	font-size: 32rpx;
}

.selector-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.enhanced-selector-input {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.enhanced-selector-input:active {
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	transform: scale(0.98);
}

.selected-user {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.selector-arrow {
	font-size: 24rpx;
	color: #666;
	transition: transform 0.3s ease;
}

/* 增强的时间选择器 */
.enhanced-time-selector {
	margin: 0 0 20rpx;
}

.time-selector-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.time-selector-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 25rpx;
}

.time-icon {
	font-size: 32rpx;
}

.time-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.enhanced-range-tabs {
	display: flex;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	padding: 8rpx;
	margin-bottom: 25rpx;
	gap: 6rpx;
}

.enhanced-range-tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6rpx;
	padding: 20rpx 10rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	background: transparent;
}

.enhanced-range-tab.active {
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: white;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.tab-icon {
	font-size: 28rpx;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

.enhanced-range-tab:not(.active) .tab-icon,
.enhanced-range-tab:not(.active) .tab-text {
	color: #666;
}

.enhanced-custom-range {
	margin-top: 25rpx;
}

.date-picker-container {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.enhanced-date-picker {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.enhanced-date-picker:active {
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	transform: scale(0.98);
}

.date-icon {
	font-size: 28rpx;
	color: #666;
}

.date-text {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.date-separator {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	border-radius: 50%;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.separator-icon {
	font-size: 24rpx;
	color: white;
	font-weight: bold;
}

/* 增强的错误状态 */
.enhanced-error-container {
	margin: 60rpx 20rpx;
}

.error-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.error-card .error-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.error-card .error-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.error-card .error-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
	line-height: 1.5;
}

.enhanced-retry-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx 48rpx;
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	font-size: 30rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;
}

.enhanced-retry-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
}

.enhanced-retry-btn .btn-icon {
	font-size: 28rpx;
}

/* 增强的操作按钮区域 */
.enhanced-action-section {
	margin: 0 0 40rpx;
}

.action-buttons-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}




.action-buttons-grid {
	display: flex;
	gap: 16rpx;
	justify-content: space-between;
}

.enhanced-action-btn {
	flex: 1;
	background: white;
	border: none;
	border-radius: 20rpx;
	padding: 0;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	position: relative;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.enhanced-action-btn::after {
	border: none;
}

.enhanced-action-btn:active {
	transform: scale(0.96);
}

.enhanced-action-btn.export-btn {
	background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
	box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.3);
}

.enhanced-action-btn.export-btn:active {
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
}

.enhanced-action-btn.print-btn {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
	box-shadow: 0 6rpx 24rpx rgba(33, 150, 243, 0.3);
}

.enhanced-action-btn.print-btn:active {
	box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.4);
}

.btn-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx 20rpx;
	min-height: 100rpx;
}

.btn-content .btn-icon {
	font-size: 40rpx;
	color: white;
	transition: transform 0.3s ease;
}

.enhanced-action-btn:active .btn-icon {
	transform: scale(1.1);
}

.btn-content .btn-text {
	font-size: 30rpx;
	font-weight: 700;
	color: white;
	letter-spacing: 0.5rpx;
}

/* 按钮悬停和交互效果 */
@media (min-width: 751rpx) {
	.enhanced-action-btn:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
	}

	.enhanced-action-btn.export-btn:hover {
		box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4);
	}

	.enhanced-action-btn.print-btn:hover {
		box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.4);
	}

	.enhanced-action-btn:hover .btn-icon {
		transform: scale(1.05);
	}
}

/* 按钮加载状态 */
.enhanced-action-btn.loading {
	opacity: 0.8;
	pointer-events: none;
}

.enhanced-action-btn.loading .btn-icon {
	animation: spin 1s linear infinite;
}

.enhanced-action-btn.loading::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.2);
	animation: pulse 1.5s ease-in-out infinite;
	border-radius: 20rpx;
}

@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes pulse {
	0%, 100% { opacity: 0; }
	50% { opacity: 1; }
}

/* 按钮焦点状态 */
.enhanced-action-btn:focus {
	outline: none;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.3);
}

.enhanced-action-btn.print-btn:focus {
	box-shadow: 0 0 0 4rpx rgba(33, 150, 243, 0.3);
}

/* 按钮内部光泽效果 */
.enhanced-action-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
	border-radius: 20rpx 20rpx 0 0;
	pointer-events: none;
}

/* 按钮禁用状态 */
.enhanced-action-btn:disabled {
	opacity: 0.5;
	pointer-events: none;
	background: #f5f5f5;
	box-shadow: none;
}

.enhanced-action-btn:disabled .btn-icon,
.enhanced-action-btn:disabled .btn-text {
	color: #999;
}

/* 增强的记录列表区域 */
.enhanced-records-section {
	margin: 0 20rpx 30rpx;
}

.records-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 0 10rpx;
}

.records-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.records-icon {
	font-size: 32rpx;
}

.records-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.records-count {
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.count-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.enhanced-record-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.enhanced-record-item {
	cursor: pointer;
	transition: all 0.3s ease;
}

.enhanced-record-item:active {
	transform: translateY(2rpx);
}

.record-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	overflow: hidden;
	position: relative;
}

.enhanced-record-item:active .record-card {
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 不同工作模式的卡片样式 */
.enhanced-record-item[data-work-mode="tea_picking"] .record-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(76, 175, 80, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #4caf50;
	box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.15);
}

.enhanced-record-item[data-work-mode="tea_picking"] .record-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

.enhanced-record-item[data-work-mode="hourly"] .record-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(33, 150, 243, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(33, 150, 243, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #2196f3;
	box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.15);
}

.enhanced-record-item[data-work-mode="hourly"] .record-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

/* 增强点击效果 */
.enhanced-record-item[data-work-mode="tea_picking"]:active .record-card {
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.25);
}

.enhanced-record-item[data-work-mode="hourly"]:active .record-card {
	background: linear-gradient(135deg, rgba(33, 150, 243, 0.08) 0%, rgba(33, 150, 243, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.25);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

.record-date-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-icon {
	font-size: 28rpx;
	color: #666;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.2;
}

.work-mode-badge {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	font-size: 26rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}

.work-mode-badge.tea_picking {
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.work-mode-badge.hourly {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.mode-icon {
	font-size: 24rpx;
}

.mode-text {
	font-size: 26rpx;
	font-weight: 600;
}

.enhanced-record-content {
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

.enhanced-record-details {
	margin-bottom: 20rpx;
}

.detail-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
}

.detail-card {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.8) 100%);
	border-radius: 12rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.enhanced-record-details.tea-picking .detail-card {
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.12) 100%);
	border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.enhanced-record-details.hourly .detail-card {
	background: linear-gradient(135deg, rgba(33, 150, 243, 0.08) 0%, rgba(33, 150, 243, 0.12) 100%);
	border: 1rpx solid rgba(33, 150, 243, 0.2);
}

.detail-icon {
	font-size: 28rpx;
	opacity: 0.8;
}

.enhanced-record-details.tea-picking .detail-icon {
	color: #4caf50;
}

.enhanced-record-details.hourly .detail-icon {
	color: #2196f3;
}

.detail-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	flex: 1;
}

.detail-label {
	font-size: 24rpx;
	color: #666;
	line-height: 1.2;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.2;
}

.enhanced-record-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 25rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 1;
}

.earnings-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.earnings-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.earnings-value {
	font-size: 36rpx;
	font-weight: 700;
	color: #2e7d32;
	text-shadow: 0 1rpx 2rpx rgba(46, 125, 50, 0.1);
}

.view-detail-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	border-radius: 25rpx;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.detail-btn-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.detail-btn-icon {
	font-size: 20rpx;
	color: white;
	font-weight: bold;
}

/* 增强的空状态 */
.enhanced-empty-state {
	margin: 60rpx 20rpx;
}

.empty-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.empty-card .empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-card .empty-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.empty-card .empty-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
	line-height: 1.5;
}

.empty-actions {
	display: flex;
	justify-content: center;
}

.empty-action-btn {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 24rpx 48rpx;
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	font-size: 30rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;
}

.empty-action-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
}

.empty-btn-icon {
	font-size: 28rpx;
}

.empty-btn-text {
	font-size: 30rpx;
	font-weight: 500;
}


/* 响应式设计 */
@media (max-width: 750rpx) {
	.page-header {
		padding: 50rpx 20rpx 30rpx;
	}

	.header-title {
		gap: 12rpx;
	}

	.title-icon {
		font-size: 40rpx;
	}

	.title-text {
		font-size: 36rpx;
	}

	.header-subtitle text {
		font-size: 26rpx;
	}

	.enhanced-user-selector,
	.enhanced-time-selector,
	.enhanced-action-section,
	.enhanced-records-section,
	.enhanced-empty-state {
		margin-left: 15rpx;
		margin-right: 15rpx;
	}

	.selector-card,
	.time-selector-card,
	.action-buttons-card {
		padding: 25rpx;
	}

	.action-buttons-grid {
		flex-direction: column;
		gap: 12rpx;
	}

	.enhanced-action-btn {
		width: 100%;
	}

	.btn-content {
		padding: 20rpx 16rpx;
		min-height: 80rpx;
		gap: 10rpx;
	}

	.btn-content .btn-icon {
		font-size: 36rpx;
	}

	.btn-content .btn-text {
		font-size: 28rpx;
	}

	.detail-grid {
		grid-template-columns: 1fr;
		gap: 12rpx;
	}

	.date-picker-container {
		flex-direction: column;
		gap: 15rpx;
	}

	.date-separator {
		transform: rotate(90deg);
	}

	.enhanced-record-footer {
		flex-direction: column;
		gap: 15rpx;
		align-items: stretch;
	}

	.view-detail-btn {
		justify-content: center;
	}
}

/* 详情弹窗样式 */
.record-detail-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 40rpx;
	animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

.modal-content {
	background-color: white;
	border-radius: 16rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 85vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(50rpx);
		opacity: 0;
	}

	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 2rpx solid #f0f0f0;
	background-color: #fafafa;
	border-radius: 16rpx 16rpx 0 0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: #f5f5f5;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:active {
	background-color: #e0e0e0;
	transform: scale(0.95);
}

.close-icon {
	font-size: 32rpx;
	color: #666;
	font-weight: bold;
}

.modal-body {
	flex: 1;
	overflow-y: auto;
	padding: 40rpx;
}

.detail-section {
	margin-bottom: 40rpx;
}

.detail-section:last-child {
	margin-bottom: 0;
}

.section-header {
	margin-bottom: 25rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

/* 基本信息网格 */
.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.info-item.full-width {
	grid-column: 1 / -1;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.work-mode-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	align-self: flex-start;
}

.work-mode-tag.tea_picking {
	background-color: rgba(76, 175, 80, 0.1);
	color: #2e7d32;
}

.work-mode-tag.hourly {
	background-color: rgba(33, 150, 243, 0.1);
	color: #2196F3;
}

/* 工作详情 */
.work-details {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
}

.time-range {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background-color: white;
	border-radius: 8rpx;
	gap: 20rpx;
}

.time-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.time-label {
	font-size: 24rpx;
	color: #666;
}

.time-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.time-separator {
	font-size: 32rpx;
	color: #999;
	font-weight: bold;
}

.detail-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.detail-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	padding: 20rpx;
	background-color: white;
	border-radius: 8rpx;
	border-left: 4rpx solid #e0e0e0;
}

.detail-item.highlight {
	border-left-color: #2e7d32;
	background-color: rgba(76, 175, 80, 0.05);
}

.detail-item.overtime {
	border-left-color: #FF9800;
	background-color: rgba(255, 152, 0, 0.05);
}

.detail-label {
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.detail-item.highlight .detail-value {
	color: #2e7d32;
}

.detail-item.overtime .detail-value {
	color: #FF9800;
}

/* 收入详情 */
.earnings-details {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
}

.earnings-breakdown {
	margin-bottom: 25rpx;
}

.earnings-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 20rpx;
	background-color: white;
	border-radius: 8rpx;
	margin-bottom: 15rpx;
	border-left: 4rpx solid #e0e0e0;
}

.earnings-item.overtime {
	border-left-color: #FF9800;
	background-color: rgba(255, 152, 0, 0.05);
}

.earnings-label {
	font-size: 28rpx;
	color: #666;
}

.earnings-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.earnings-item.overtime .earnings-value {
	color: #FF9800;
}

.total-earnings {
	background: linear-gradient(135deg, #2e7d32, #45a049);
	color: white;
	padding: 25rpx;
	border-radius: 12rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.3);
}

.total-label {
	font-size: 32rpx;
	font-weight: 600;
}

.total-value {
	font-size: 36rpx;
	font-weight: 700;
}

/* 备注内容 */
.notes-content {
	background-color: #f8f9fa;
	padding: 25rpx;
	border-radius: 12rpx;
	border-left: 4rpx solid #2e7d32;
}

.notes-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 移动端适配 */
@media (max-width: 750rpx) {
	.record-detail-modal {
		padding: 20rpx;
	}

	.modal-content {
		width: 95%;
		max-height: 90vh;
	}

	.modal-header {
		padding: 25rpx 30rpx;
	}

	.modal-body {
		padding: 30rpx;
	}

	.info-grid,
	.detail-grid {
		grid-template-columns: 1fr;
		gap: 15rpx;
	}

	.time-range {
		flex-direction: column;
		gap: 15rpx;
	}

	.time-separator {
		transform: rotate(90deg);
	}

	.detail-section {
		margin-bottom: 30rpx;
	}

	.section-title {
		font-size: 30rpx;
	}

	.info-label,
	.detail-label,
	.earnings-label {
		font-size: 26rpx;
	}

	.info-value,
	.detail-value,
	.earnings-value {
		font-size: 30rpx;
	}

	.total-label {
		font-size: 30rpx;
	}

	.total-value {
		font-size: 34rpx;
	}
}

/* 列表项点击反馈已在上面定义 */
</style>
