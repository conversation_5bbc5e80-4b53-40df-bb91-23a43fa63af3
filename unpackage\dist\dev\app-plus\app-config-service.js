
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#f5f5f5","background":"#f5f5f5","navigationBar":{"backgroundColor":"#2e7d32","titleText":"采茶记录管理系统","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"TeaApp","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#2e7d32","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","iconPath":"/static/icons/home.png","selectedIconPath":"/static/icons/home-active.png","text":"首页"},{"pagePath":"pages/record/list","iconPath":"/static/icons/list.png","selectedIconPath":"/static/icons/list-active.png","text":"记录"},{"pagePath":"pages/statistics/personal","iconPath":"/static/icons/chart.png","selectedIconPath":"/static/icons/chart-active.png","text":"统计"},{"pagePath":"pages/profile/profile","iconPath":"/static/icons/profile.png","selectedIconPath":"/static/icons/profile-active.png","text":"我的"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"style":"custom","type":"default"},"isNVue":false}},{"path":"pages/login/login","meta":{"navigationBar":{"backgroundColor":"#2e7d32","titleText":"登录","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/register/register","meta":{"navigationBar":{"backgroundColor":"#2e7d32","titleText":"注册","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/record/list","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#2e7d32","titleText":"记录列表","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/record/add","meta":{"navigationBar":{"backgroundColor":"#2e7d32","titleText":"添加记录","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/record/detail-tea","meta":{"navigationBar":{"style":"custom","type":"default"},"isNVue":false}},{"path":"pages/record/detail-hourly","meta":{"navigationBar":{"style":"custom","type":"default"},"isNVue":false}},{"path":"pages/statistics/personal","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#2e7d32","titleText":"个人统计","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/profile/profile","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"navigationBar":{"backgroundColor":"#2e7d32","titleText":"个人中心","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/profile/name-library","meta":{"navigationBar":{"backgroundColor":"#2e7d32","titleText":"姓名库管理","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/profile/comprehensive","meta":{"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#2e7d32","titleText":"综合统计","type":"default","titleColor":"#ffffff"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  