/**
 * 本地数据库管理工具
 * 使用uni-app的本地存储API模拟SQLite数据库功能
 */

class LocalDatabase {
	constructor() {
		this.dbName = 'tea_record_db'
		this.version = '2.0.0' // 更新版本号：支持多客户销售模式
		this.tables = {
			workRecords: 'workRecords',
			sales_records: 'sales_records',
			name_library: 'name_library',
			sync_queue: 'sync_queue',
			app_config: 'app_config'
		}
		this.init()
	}
	
	/**
	 * 初始化数据库
	 */
	init() {
		try {
			// 检查数据库版本
			const dbInfo = this.getDbInfo()
			const needsMigration = !dbInfo || dbInfo.version !== this.version

			if (needsMigration) {
				// 执行数据迁移
				if (dbInfo && dbInfo.version) {
					this.migrateDatabase(dbInfo.version, this.version)
				}

				this.createTables()
				this.setDbInfo({
					name: this.dbName,
					version: this.version,
					created_at: dbInfo?.created_at || new Date().toISOString(),
					updated_at: new Date().toISOString()
				})
			}

			console.log('本地数据库初始化成功，版本:', this.version)
		} catch (error) {
			console.error('数据库初始化失败:', error)
		}
	}
	
	/**
	 * 创建表结构
	 */
	createTables() {
		// 初始化各个表的存储
		Object.values(this.tables).forEach(tableName => {
			if (!uni.getStorageSync(tableName)) {
				uni.setStorageSync(tableName, [])
			}
		})
		
		// 初始化姓名库
		if (!uni.getStorageSync('nameLibrary')) {
			uni.setStorageSync('nameLibrary', [])
		}
		
		// 初始化应用配置
		if (!uni.getStorageSync('appConfig')) {
			uni.setStorageSync('appConfig', {
				syncInterval: 30000,
				maxLocalRecords: 10000,
				enableOfflineMode: true
			})
		}
	}
	
	/**
	 * 获取数据库信息
	 */
	getDbInfo() {
		return uni.getStorageSync('db_info')
	}
	
	/**
	 * 设置数据库信息
	 */
	setDbInfo(info) {
		uni.setStorageSync('db_info', info)
	}
	
	/**
	 * 插入记录
	 */
	insert(tableName, data) {
		try {
			const records = uni.getStorageSync(tableName) || []
			const newRecord = {
				id: this.generateId(),
				...data,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				sync_status: 'pending' // pending, synced, error
			}
			
			records.unshift(newRecord)
			
			// 检查记录数量限制
			const maxRecords = this.getMaxRecords(tableName)
			if (records.length > maxRecords) {
				records.splice(maxRecords)
			}
			
			uni.setStorageSync(tableName, records)
			
			// 添加到同步队列
			this.addToSyncQueue('insert', tableName, newRecord)
			
			return newRecord
		} catch (error) {
			console.error('插入记录失败:', error)
			throw error
		}
	}
	
	/**
	 * 更新记录
	 */
	update(tableName, id, data) {
		try {
			const records = uni.getStorageSync(tableName) || []
			const index = records.findIndex(record => record.id === id)
			
			if (index === -1) {
				throw new Error('记录不存在')
			}
			
			const updatedRecord = {
				...records[index],
				...data,
				updated_at: new Date().toISOString(),
				sync_status: 'pending'
			}
			
			records[index] = updatedRecord
			uni.setStorageSync(tableName, records)
			
			// 添加到同步队列
			this.addToSyncQueue('update', tableName, updatedRecord)
			
			return updatedRecord
		} catch (error) {
			console.error('更新记录失败:', error)
			throw error
		}
	}
	
	/**
	 * 删除记录
	 */
	delete(tableName, id) {
		try {
			const records = uni.getStorageSync(tableName) || []
			const index = records.findIndex(record => record.id === id)
			
			if (index === -1) {
				throw new Error('记录不存在')
			}
			
			const deletedRecord = records[index]
			records.splice(index, 1)
			uni.setStorageSync(tableName, records)
			
			// 添加到同步队列
			this.addToSyncQueue('delete', tableName, { id })
			
			return deletedRecord
		} catch (error) {
			console.error('删除记录失败:', error)
			throw error
		}
	}
	
	/**
	 * 查询记录
	 */
	select(tableName, options = {}) {
		try {
			let records = uni.getStorageSync(tableName) || []
			
			// 应用筛选条件
			if (options.where) {
				records = records.filter(record => {
					return Object.keys(options.where).every(key => {
						const value = options.where[key]
						if (typeof value === 'object' && value.like) {
							return record[key] && record[key].includes(value.like)
						}
						return record[key] === value
					})
				})
			}
			
			// 排序
			if (options.orderBy) {
				const { field, direction = 'desc' } = options.orderBy
				records.sort((a, b) => {
					if (direction === 'asc') {
						return a[field] > b[field] ? 1 : -1
					} else {
						return a[field] < b[field] ? 1 : -1
					}
				})
			}
			
			// 分页
			if (options.limit) {
				const offset = options.offset || 0
				records = records.slice(offset, offset + options.limit)
			}
			
			return records
		} catch (error) {
			console.error('查询记录失败:', error)
			throw error
		}
	}
	
	/**
	 * 统计记录数量
	 */
	count(tableName, where = {}) {
		try {
			const records = this.select(tableName, { where })
			return records.length
		} catch (error) {
			console.error('统计记录失败:', error)
			throw error
		}
	}
	
	/**
	 * 添加到同步队列
	 */
	addToSyncQueue(operation, tableName, data) {
		try {
			const syncQueue = uni.getStorageSync('sync_queue') || []
			const syncItem = {
				id: this.generateId(),
				operation, // insert, update, delete
				table: tableName,
				data,
				timestamp: new Date().toISOString(),
				retry_count: 0,
				status: 'pending' // pending, syncing, success, error
			}
			
			syncQueue.push(syncItem)
			uni.setStorageSync('sync_queue', syncQueue)
		} catch (error) {
			console.error('添加同步队列失败:', error)
		}
	}
	
	/**
	 * 获取同步队列
	 */
	getSyncQueue() {
		return uni.getStorageSync('sync_queue') || []
	}
	
	/**
	 * 清空同步队列
	 */
	clearSyncQueue() {
		uni.setStorageSync('sync_queue', [])
	}
	
	/**
	 * 移除同步队列项
	 */
	removeSyncQueueItem(id) {
		const syncQueue = uni.getStorageSync('sync_queue') || []
		const filteredQueue = syncQueue.filter(item => item.id !== id)
		uni.setStorageSync('sync_queue', filteredQueue)
	}
	
	/**
	 * 生成唯一ID
	 */
	generateId() {
		return Date.now().toString() + Math.random().toString(36).substr(2, 9)
	}
	
	/**
	 * 获取表的最大记录数
	 */
	getMaxRecords(tableName) {
		const config = uni.getStorageSync('appConfig') || {}
		return config.maxLocalRecords || 10000
	}
	
	/**
	 * 清理过期数据
	 */
	cleanup() {
		try {
			const maxAge = 30 * 24 * 60 * 60 * 1000 // 30天
			const cutoffDate = new Date(Date.now() - maxAge).toISOString()
			
			Object.values(this.tables).forEach(tableName => {
				const records = uni.getStorageSync(tableName) || []
				const filteredRecords = records.filter(record => 
					record.created_at > cutoffDate || record.sync_status === 'pending'
				)
				uni.setStorageSync(tableName, filteredRecords)
			})
			
			console.log('数据清理完成')
		} catch (error) {
			console.error('数据清理失败:', error)
		}
	}
	
	/**
	 * 获取数据库统计信息
	 */
	getStats() {
		const stats = {}
		
		Object.entries(this.tables).forEach(([key, tableName]) => {
			const records = uni.getStorageSync(tableName) || []
			stats[key] = {
				total: records.length,
				pending_sync: records.filter(r => r.sync_status === 'pending').length
			}
		})
		
		stats.sync_queue = this.getSyncQueue().length
		
		return stats
	}

	// ==================== 销售记录专用方法 ====================

	/**
	 * 创建销售记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 创建的销售记录
	 */
	createSalesRecord(salesData) {
		return this.insert('sales_records', salesData)
	}

	/**
	 * 根据工作记录ID获取销售记录
	 * @param {String} workRecordId 工作记录ID
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByWorkRecordId(workRecordId) {
		return this.select('sales_records', {
			where: { work_record_id: workRecordId.toString() },
			orderBy: { field: 'created_at', direction: 'desc' }
		})
	}

	/**
	 * 根据日期获取销售记录
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByDate(date) {
		const allSalesRecords = this.select('sales_records')
		const workRecords = uni.getStorageSync('workRecords') || []

		return allSalesRecords.filter(salesRecord => {
			const workRecord = workRecords.find(wr => wr.id === salesRecord.work_record_id)
			return workRecord && workRecord.date === date
		})
	}

	/**
	 * 更新销售记录
	 * @param {String} salesRecordId 销售记录ID
	 * @param {Object} updateData 更新数据
	 * @returns {Object} 更新后的销售记录
	 */
	updateSalesRecord(salesRecordId, updateData) {
		return this.update('sales_records', salesRecordId, updateData)
	}

	/**
	 * 删除销售记录
	 * @param {String} salesRecordId 销售记录ID
	 * @returns {Object} 删除的销售记录
	 */
	deleteSalesRecord(salesRecordId) {
		return this.delete('sales_records', salesRecordId)
	}

	/**
	 * 获取所有销售记录
	 * @param {Object} options 查询选项
	 * @returns {Array} 销售记录列表
	 */
	getAllSalesRecords(options = {}) {
		return this.select('sales_records', {
			orderBy: { field: 'created_at', direction: 'desc' },
			...options
		})
	}

	/**
	 * 根据客户名称获取销售记录
	 * @param {String} customerName 客户名称
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByCustomer(customerName) {
		return this.select('sales_records', {
			where: { customer: { like: customerName } },
			orderBy: { field: 'created_at', direction: 'desc' }
		})
	}

	/**
	 * 数据库迁移
	 * @param {String} fromVersion 源版本
	 * @param {String} toVersion 目标版本
	 */
	migrateDatabase(fromVersion, toVersion) {
		console.log(`开始数据库迁移: ${fromVersion} -> ${toVersion}`)

		try {
			// 从 1.x.x 迁移到 2.0.0
			if (fromVersion.startsWith('1.') && toVersion === '2.0.0') {
				this.migrateTo2_0_0()
			}

			console.log('数据库迁移完成')
		} catch (error) {
			console.error('数据库迁移失败:', error)
			throw error
		}
	}

	/**
	 * 迁移到版本 2.0.0 - 支持多客户销售模式
	 */
	migrateTo2_0_0() {
		console.log('开始迁移到版本 2.0.0...')

		// 迁移销售记录表
		const salesRecords = uni.getStorageSync('sales_records') || []
		let migratedCount = 0

		salesRecords.forEach(record => {
			// 为现有记录添加新字段
			if (!record.sales_mode) {
				record.sales_mode = 'single_customer' // 默认为单客户模式
				record.is_master_record = true
				record.allocation_strategy = 'auto'
				record.updated_at = new Date().toISOString()
				migratedCount++
			}
		})

		// 保存迁移后的数据
		uni.setStorageSync('sales_records', salesRecords)

		console.log(`销售记录迁移完成，共迁移 ${migratedCount} 条记录`)
	}

	/**
	 * 获取销售记录统计信息
	 * @param {Object} dateRange 日期范围 {startDate, endDate}
	 * @returns {Object} 统计信息
	 */
	getSalesRecordsStats(dateRange = {}) {
		let salesRecords = this.getAllSalesRecords()

		// 如果指定了日期范围，进行筛选
		if (dateRange.startDate && dateRange.endDate) {
			const workRecords = uni.getStorageSync('workRecords') || []
			salesRecords = salesRecords.filter(salesRecord => {
				const workRecord = workRecords.find(wr => wr.id === salesRecord.work_record_id)
				if (!workRecord || !workRecord.date) return false

				const recordDate = new Date(workRecord.date)
				const startDate = new Date(dateRange.startDate)
				const endDate = new Date(dateRange.endDate)

				return recordDate >= startDate && recordDate <= endDate
			})
		}

		return {
			totalRecords: salesRecords.length,
			totalCost: salesRecords.reduce((sum, record) => sum + (record.total_cost || 0), 0),
			totalProduction: salesRecords.reduce((sum, record) => sum + (record.production || 0), 0),
			avgSellingPrice: salesRecords.length > 0 ?
				salesRecords.reduce((sum, record) => sum + (record.selling_price || 0), 0) / salesRecords.length : 0
		}
	}
}

// 导出类，让调用者自己实例化
export default LocalDatabase
